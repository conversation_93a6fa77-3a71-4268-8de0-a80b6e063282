package com.airdoc.research.controller;

import com.airdoc.research.common.Result;
import com.airdoc.research.dto.FileUploadResponseDTO;
import com.airdoc.research.service.FileUploadService;
import com.airdoc.research.service.EmTestRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/files")
@Validated
public class FileUploadController {

    @Resource
    private FileUploadService fileUploadService;

    @Resource
    private EmTestRecordService testRecordService;

    /**
     * 上传图片
     * @param file 上传的图片文件
     * @return 上传结果
     */
    @PostMapping("/upload/image")
    public Result<FileUploadResponseDTO> uploadImage(@RequestParam("file") MultipartFile file) {
        log.info("接收到图片上传请求，文件名：{}", file.getOriginalFilename());

        try {
            FileUploadResponseDTO responseDTO = fileUploadService.uploadImage(file);
            return Result.success("图片上传成功", responseDTO);
        } catch (Exception e) {
            log.error("图片上传失败", e);
            return Result.error("图片上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传图片并更新检测记录
     * @param file 上传的图片文件
     * @param recordId 检测记录ID
     * @return 上传结果
     */
    @PostMapping("/upload/image/test-record/{recordId}")
    public Result<FileUploadResponseDTO> uploadImageForTestRecord(
            @RequestParam("file") MultipartFile file,
            @PathVariable Long recordId) {
        log.info("接收到检测记录图片上传请求，记录ID：{}，文件名：{}", recordId, file.getOriginalFilename());

        try {
            // 检查检测记录是否存在
            if (!testRecordService.existsById(recordId)) {
                return Result.badRequest("检测记录不存在，ID：" + recordId);
            }

            // 上传图片
            FileUploadResponseDTO responseDTO = fileUploadService.uploadImage(file);

            // 更新检测记录的图片URL
            boolean updated = testRecordService.updateImageUrl(recordId, responseDTO.getUrl());
            if (!updated) {
                // 如果更新失败，删除已上传的文件
                fileUploadService.deleteFile(responseDTO.getFileName());
                return Result.error("更新检测记录图片URL失败");
            }

            log.info("检测记录图片上传并更新成功，记录ID：{}，图片URL：{}", recordId, responseDTO.getUrl());
            return Result.success("检测记录图片上传成功", responseDTO);

        } catch (Exception e) {
            log.error("检测记录图片上传失败，记录ID：{}", recordId, e);
            return Result.error("检测记录图片上传失败：" + e.getMessage());
        }
    }

    /**
     * 下载/访问文件
     * @param fileName 文件名
     * @return 文件内容
     */
    @GetMapping("/{fileName}")
    public ResponseEntity<FileSystemResource> downloadFile(@PathVariable String fileName) {
        try {
            // 检查文件是否存在
            if (!fileUploadService.fileExists(fileName)) {
                return ResponseEntity.notFound().build();
            }

            // 构建文件路径
            Path filePath = Paths.get("uploads", fileName);
            File file = filePath.toFile();

            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 创建文件资源
            FileSystemResource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName);
            
            // 根据文件扩展名设置Content-Type
            String contentType = getContentType(fileName);
            MediaType mediaType = MediaType.parseMediaType(contentType);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(mediaType)
                    .body(resource);

        } catch (Exception e) {
            log.error("文件下载失败：{}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除文件
     * @param fileName 文件名
     * @return 删除结果
     */
    @DeleteMapping("/{fileName}")
    public Result<Boolean> deleteFile(@PathVariable String fileName) {
        log.info("接收到文件删除请求，文件名：{}", fileName);

        try {
            boolean deleted = fileUploadService.deleteFile(fileName);
            if (deleted) {
                return Result.success("文件删除成功", true);
            } else {
                return Result.error("文件不存在或删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败：{}", fileName, e);
            return Result.error("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     * @param fileName 文件名
     * @return 是否存在
     */
    @GetMapping("/{fileName}/exists")
    public Result<Boolean> fileExists(@PathVariable String fileName) {
        try {
            boolean exists = fileUploadService.fileExists(fileName);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查文件是否存在失败：{}", fileName, e);
            return Result.error("检查文件是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> "image/jpeg";
            case "png" -> "image/png";
            case "gif" -> "image/gif";
            case "bmp" -> "image/bmp";
            case "webp" -> "image/webp";
            default -> "application/octet-stream";
        };
    }
}
